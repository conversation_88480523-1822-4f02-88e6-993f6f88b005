/// Simple Token Refresh Service
/// WhatsApp/Instagram tarzı minimal token yönetimi
library;

import 'dart:async';
import 'package:flutter/foundation.dart';
import '../constants/app_constants.dart';
import '../models/models.dart';
import 'services.dart';

/// Simple Token Refresh Service
class TokenRefreshService {
  static final TokenRefreshService _instance = TokenRefreshService._internal();
  factory TokenRefreshService() => _instance;
  TokenRefreshService._internal();

  // Services
  final StorageService _storageService = StorageService();
  final JwtService _jwtService = JwtService();
  final ApiService _apiService = ApiService();

  // Timer management
  Timer? _refreshTimer;
  bool _isRefreshing = false;
  bool _isActive = false;

  // Callbacks
  VoidCallback? _onTokenRefreshed;
  VoidCallback? _onRefreshFailed;

  /// Service'in aktif olup olmadığını kontrol et
  bool get isActive => _isActive;

  /// Service'i başlat
  Future<void> initialize({
    VoidCallback? onTokenRefreshed,
    VoidCallback? onRefreshFailed,
  }) async {
    try {
      _onTokenRefreshed = onTokenRefreshed;
      _onRefreshFailed = onRefreshFailed;
      _isActive = true;

      await _startTokenTimer();
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'TokenRefreshService initialize');
    }
  }

  /// Token timer'ını başlat
  Future<void> _startTokenTimer() async {
    try {
      final accessToken = await _storageService.getAccessToken();
      if (accessToken == null) return;

      if (!_jwtService.isTokenValid(accessToken)) {
        await _refreshToken();
        return;
      }

      final remainingTime = _jwtService.getTokenRemainingTime(accessToken);
      if (remainingTime == null) return;

      // 1 dakika kala refresh et
      final timeUntilRefresh = remainingTime - AppConstants.tokenRefreshThreshold;

      _refreshTimer?.cancel();

      if (timeUntilRefresh.isNegative) {
        await _refreshToken();
        return;
      }

      _refreshTimer = Timer(timeUntilRefresh, _refreshToken);
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'TokenRefreshService _startTokenTimer');
    }
  }

  /// Token'ı yenile
  Future<void> _refreshToken() async {
    if (_isRefreshing || !_isActive) return;

    try {
      _isRefreshing = true;

      final refreshToken = await _storageService.getRefreshToken();
      if (refreshToken == null) {
        _onRefreshFailed?.call();
        return;
      }

      // Device info al
      DeviceInfo? deviceInfo = await _storageService.getDeviceInfo();
      if (deviceInfo == null) {
        final deviceService = DeviceService();
        deviceInfo = await deviceService.getDeviceInfo();
        await _storageService.saveDeviceInfo(deviceInfo);
      }

      // API çağrısı
      final response = await _apiService.post<Map<String, dynamic>>(
        '/auth/refresh-token',
        data: {
          'refreshToken': refreshToken,
          'deviceInfo': deviceInfo.toDeviceInfoString(),
        },
        isAuthFree: true,
      );

      if (response.statusCode == 200 && response.data?['success'] == true) {
        final data = response.data!['data'];
        
        // Token'ları kaydet
        await _storageService.saveAccessToken(data['token']);
        await _storageService.saveRefreshToken(data['refreshToken']);

        // User data'yı güncelle
        final userModel = _jwtService.decodeToken(data['token']);
        if (userModel != null) {
          await _storageService.saveUserData(userModel);
        }

        _onTokenRefreshed?.call();
        await _startTokenTimer(); // Yeni timer başlat
      } else {
        _onRefreshFailed?.call();
      }
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'TokenRefreshService _refreshToken');
      _onRefreshFailed?.call();
    } finally {
      _isRefreshing = false;
    }
  }

  /// Service'i durdur
  void stop() {
    _refreshTimer?.cancel();
    _refreshTimer = null;
    _isActive = false;
    _isRefreshing = false;
  }

  /// Manuel refresh
  Future<bool> triggerManualRefresh() async {
    if (!_isActive) return false;
    
    try {
      await _refreshToken();
      return true;
    } catch (e) {
      return false;
    }
  }

  /// App resume'da token kontrolü
  Future<void> onAppResume() async {
    if (!_isActive) return;
    
    try {
      final accessToken = await _storageService.getAccessToken();
      if (accessToken == null) return;

      if (_jwtService.shouldRefreshToken(accessToken)) {
        await _refreshToken();
      }
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'TokenRefreshService onAppResume');
    }
  }

  void dispose() => stop();
}
